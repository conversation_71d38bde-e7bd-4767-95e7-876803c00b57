# Wallet Switching Fix - MetaMask Account Selection

## Vấn đề
Trước đây, khi người dùng disconnect wallet và connect lại, <PERSON>ng dụng tự động kết nối với tài khoản MetaMask cuối cùng mà không cho phép chọn tài khoản khác. Điều này gây khó khăn khi muốn chuyển đổi giữa các tài khoản MetaMask.

## Giải pháp đã triển khai

### 1. Cải thiện Logic Disconnect
**File:** `app/contexts/Web3Context.tsx`

#### Thay đổi chính:
- **Revoke Permissions:** Thử gọi `wallet_revokePermissions` để xóa quyền truy cập
- **Disconnect Timestamp:** <PERSON><PERSON>u thời gian disconnect để tránh auto-reconnect ngay lập tức
- **Clear State:** Xóa hoàn toàn state và localStorage

```typescript
const disconnectWallet = useCallback(async () => {
  try {
    // Clear state
    dispatch({ type: 'WALLET_DISCONNECTED' });
    
    // Clear localStorage and set disconnect time
    localStorage.removeItem('wallet_connected');
    localStorage.removeItem('wallet_address');
    localStorage.setItem('wallet_disconnect_time', Date.now().toString());
    
    // Try to revoke permissions
    await window.ethereum.request({
      method: 'wallet_revokePermissions',
      params: [{ eth_accounts: {} }]
    });
  } catch (error) {
    // Handle gracefully if revoke fails
  }
}, []);
```

### 2. Thêm Method Connect With Selector
**File:** `app/contexts/Web3Context.tsx`

#### Tính năng mới:
- **Force Account Selection:** Luôn hiển thị account selector
- **Clear Cache:** Xóa cache trước khi connect
- **Fresh Connection:** Đảm bảo kết nối mới hoàn toàn

```typescript
const connectWalletWithSelector = useCallback(async () => {
  // Clear any cached connection state
  localStorage.removeItem('wallet_connected');
  localStorage.removeItem('wallet_address');
  localStorage.removeItem('wallet_disconnect_time');

  // Request accounts - shows account selector
  const accounts = await window.ethereum.request({
    method: 'eth_requestAccounts',
  });
  
  // Continue with connection...
}, []);
```

### 3. Cải thiện Auto-Connect Logic
**File:** `app/contexts/Web3Context.tsx`

#### Thay đổi:
- **Respect Disconnect:** Không auto-connect nếu user vừa disconnect (trong 5 phút)
- **Address Validation:** Chỉ auto-connect nếu address cũ vẫn có sẵn
- **Smart Timing:** Kiểm tra thời gian disconnect để quyết định

```typescript
const autoConnect = async () => {
  const lastDisconnectTime = localStorage.getItem('wallet_disconnect_time');
  
  // Don't auto-connect if user disconnected recently (within 5 minutes)
  if (lastDisconnectTime) {
    const disconnectTime = parseInt(lastDisconnectTime);
    const now = Date.now();
    const fiveMinutes = 5 * 60 * 1000;
    
    if (now - disconnectTime < fiveMinutes) {
      return; // Skip auto-connect
    }
  }
  
  // Continue with auto-connect logic...
};
```

### 4. Cải thiện UI Wallet Component
**File:** `app/components/WalletConnect.tsx`

#### Tính năng mới:
- **Switch Account Button:** Nút chuyển đổi tài khoản
- **Dropdown Menu:** Menu gọn gàng với các tùy chọn
- **Better UX:** Click outside để đóng dropdown

#### Menu Options:
1. **Refresh Balances:** Làm mới số dư
2. **Add DTCERC Token:** Thêm token vào MetaMask
3. **Switch Account:** Chuyển đổi tài khoản (NEW)
4. **Disconnect:** Ngắt kết nối

```typescript
// Switch Account Handler
const handleSwitchAccount = async () => {
  setIsConnecting(true);
  try {
    await connectWalletWithSelector();
  } catch (error) {
    console.error('Failed to switch account:', error);
  } finally {
    setIsConnecting(false);
  }
};
```

## Cách sử dụng

### 1. Chuyển đổi tài khoản
1. Click vào icon "⋮" bên cạnh thông tin wallet
2. Chọn "Switch Account"
3. MetaMask sẽ hiển thị account selector
4. Chọn tài khoản mong muốn

### 2. Disconnect và Connect lại
1. Click "Disconnect" từ dropdown menu
2. Click "Connect Wallet" 
3. MetaMask sẽ hiển thị account selector
4. Chọn tài khoản mong muốn

### 3. Auto-Connect Behavior
- **Ngay sau disconnect:** Không auto-connect (trong 5 phút)
- **Sau 5 phút:** Auto-connect với tài khoản cuối cùng (nếu vẫn có sẵn)
- **Tài khoản không có sẵn:** Không auto-connect

## Lợi ích

### 1. User Experience
- ✅ Dễ dàng chuyển đổi giữa các tài khoản MetaMask
- ✅ Không bị "lock" vào một tài khoản
- ✅ UI gọn gàng với dropdown menu
- ✅ Feedback rõ ràng khi switching

### 2. Technical Benefits
- ✅ Proper state management
- ✅ Graceful error handling
- ✅ Memory leak prevention
- ✅ Consistent behavior across browsers

### 3. Security
- ✅ Proper permission revocation
- ✅ Clear session state
- ✅ No persistent unauthorized connections

## Kiểm tra

### Test Cases:
1. **Connect → Disconnect → Connect:** Hiển thị account selector
2. **Switch Account:** Chuyển đổi thành công
3. **Multiple Accounts:** Hoạt động với nhiều tài khoản
4. **Auto-Connect:** Respect disconnect timing
5. **Error Handling:** Graceful fallback khi revoke fails

### Browser Compatibility:
- ✅ Chrome + MetaMask
- ✅ Firefox + MetaMask  
- ✅ Edge + MetaMask
- ✅ Safari + MetaMask (limited)

## Lưu ý kỹ thuật

### MetaMask Limitations:
- `wallet_revokePermissions` không được hỗ trợ trên tất cả phiên bản
- Một số wallet khác có thể có behavior khác
- Auto-connect timing có thể cần điều chỉnh theo nhu cầu

### Future Improvements:
- Support cho các wallet khác (WalletConnect, Coinbase Wallet)
- Persistent account preferences
- Advanced permission management
- Multi-wallet support

## Troubleshooting

### Vấn đề thường gặp:
1. **Account selector không hiện:** Thử refresh page và connect lại
2. **Auto-connect vẫn xảy ra:** Kiểm tra localStorage và clear manually
3. **Switch account không hoạt động:** Kiểm tra MetaMask version và permissions

### Debug Commands:
```javascript
// Clear all wallet data
localStorage.removeItem('wallet_connected');
localStorage.removeItem('wallet_address');
localStorage.removeItem('wallet_disconnect_time');

// Check current state
console.log('Wallet State:', {
  connected: localStorage.getItem('wallet_connected'),
  address: localStorage.getItem('wallet_address'),
  disconnectTime: localStorage.getItem('wallet_disconnect_time')
});
```
