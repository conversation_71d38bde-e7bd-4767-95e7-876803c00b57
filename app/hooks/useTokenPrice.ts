'use client';

import { useState, useEffect } from 'react';

export interface TokenPriceData {
  dtcPrice: number;
  dtcercPrice: number;
  lastUpdated: Date;
}

export const useTokenPrice = () => {
  const [priceData, setPriceData] = useState<TokenPriceData>({
    dtcPrice: 1.0, // Default price for DTC
    dtcercPrice: 1.0, // Default price for DTCERC
    lastUpdated: new Date(),
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTokenPrices = async () => {
    setLoading(true);
    setError(null);

    try {
      // In a real application, you would fetch from a price API
      // For now, we'll use mock data with some variation
      const mockPrices: TokenPriceData = {
        dtcPrice: 1.0 + (Math.random() * 0.1 - 0.05), // $1.00 ± 5%
        dtcercPrice: 1.0 + (Math.random() * 0.1 - 0.05), // $1.00 ± 5%
        lastUpdated: new Date(),
      };

      setPriceData(mockPrices);
    } catch (err) {
      console.error('Failed to fetch token prices:', err);
      setError('Failed to fetch token prices');
      
      // Fallback to default prices
      setPriceData({
        dtcPrice: 1.0,
        dtcercPrice: 1.0,
        lastUpdated: new Date(),
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch prices on mount and set up periodic updates
  useEffect(() => {
    fetchTokenPrices();

    // Update prices every 30 seconds
    const interval = setInterval(fetchTokenPrices, 30000);

    return () => clearInterval(interval);
  }, []);

  // Calculate USD value for a given amount and token type
  const calculateUSDValue = (amount: string, tokenType: 'dtc' | 'dtcerc'): number => {
    const numAmount = parseFloat(amount) || 0;
    const price = tokenType === 'dtc' ? priceData.dtcPrice : priceData.dtcercPrice;
    return numAmount * price;
  };

  // Calculate total portfolio value
  const calculateTotalValue = (dtcAmount: string, dtcercAmount: string): number => {
    return calculateUSDValue(dtcAmount, 'dtc') + calculateUSDValue(dtcercAmount, 'dtcerc');
  };

  return {
    priceData,
    loading,
    error,
    fetchTokenPrices,
    calculateUSDValue,
    calculateTotalValue,
  };
};
