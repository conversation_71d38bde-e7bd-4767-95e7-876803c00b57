import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const keyName = searchParams.get('keyName');
    const ethAddress = searchParams.get('ethAddress');

    if (!keyName || !ethAddress) {
      throw new Error('Missing keyName or ethAddress');
    }

    // Get cosmos address
    let cosmosAddress = '';
    try {
      const showKeyCommand = `ethermintd keys show ${keyName} --keyring-backend test --output json`;
      const { stdout } = await execAsync(showKeyCommand);
      const keyInfo = JSON.parse(stdout);
      cosmosAddress = keyInfo.address;
    } catch (error) {
      return NextResponse.json({ success: false, error: 'Cosmos key not found' });
    }

    // Get balance
    const balanceCommand = `ethermintd query bank balances ${cosmosAddress} --output json`;
    const { stdout: balanceOutput } = await execAsync(balanceCommand);
    const balanceData = JSON.parse(balanceOutput);
    const aphotonBalance = balanceData.balances.find((b: any) => b.denom === 'aphoton');
    const cosmosBalance = aphotonBalance ? (parseFloat(aphotonBalance.amount) / 1e18).toString() : '0';

    // Get delegations
    const delegationsCommand = `ethermintd query staking delegations ${cosmosAddress} --output json`;
    const { stdout: delegationsOutput } = await execAsync(delegationsCommand);
    const delegationsData = JSON.parse(delegationsOutput);
    const totalDelegated = delegationsData.delegation_responses?.reduce((sum: number, del: any) => {
      return sum + parseFloat(del.balance.amount);
    }, 0) || 0;

    // Get rewards
    const rewardsCommand = `ethermintd query distribution rewards ${cosmosAddress} --output json`;
    const { stdout: rewardsOutput } = await execAsync(rewardsCommand);
    const rewardsData = JSON.parse(rewardsOutput);
    const totalRewards = rewardsData.rewards?.reduce((sum: number, reward: any) => {
      return sum + (reward.reward?.[0] ? parseFloat(reward.reward[0].amount) : 0);
    }, 0) || 0;

    // Get unbonding delegations
    const unbondingCommand = `ethermintd query staking unbonding-delegations ${cosmosAddress} --output json`;
    const { stdout: unbondingOutput } = await execAsync(unbondingCommand);
    const unbondingData = JSON.parse(unbondingOutput);
    const unbonding = unbondingData.unbonding_responses?.flatMap((ub: any) => 
      ub.entries.map((entry: any) => ({
        validator: ub.validator_address,
        amount: (parseFloat(entry.balance) / 1e18).toString(),
        completionTime: entry.completion_time
      }))
    ) || [];

    const stakingInfo = {
      delegated: (totalDelegated / 1e18).toString(),
      rewards: (totalRewards / 1e18).toString(),
      unbonding
    };

    return NextResponse.json({ 
      success: true, 
      stakingInfo, 
      cosmosAddress, 
      cosmosBalance 
    });
  } catch (error: any) {
    return NextResponse.json({ success: false, error: error.message });
  }
}