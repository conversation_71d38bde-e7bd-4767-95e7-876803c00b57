'use client';

import React, { useState, useEffect } from 'react';
import { useWeb3 } from '../contexts/Web3Context';
import { Card, Button, Input, LoadingSpinner } from './ui';
import { createCosmosKeyFromSignature } from '../actions/cosmos';
import { ethers } from 'ethers';

interface Validator {
  address: string;
  moniker: string;
  commission: string;
  status: string;
  tokens: string;
}

interface StakingInfo {
  delegated: string;
  rewards: string;
  unbonding: Array<{
    validator: string;
    amount: string;
    completionTime: string;
  }>;
}

export default function ValidatorStaking() {
  const { wallet, provider } = useWeb3();
  const [validators, setValidators] = useState<Validator[]>([]);
  const [stakingInfo, setStakingInfo] = useState<StakingInfo | null>(null);
  const [cosmosAddress, setCosmosAddress] = useState<string>('');
  const [cosmosBalance, setCosmosBalance] = useState<string>('0');
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'validators' | 'staking' | 'rewards' | 'transfer'>('validators');
  
  // Form states
  const [delegateAmount, setDelegateAmount] = useState('');
  const [undelegateAmount, setUndelegateAmount] = useState('');
  const [transferAmount, setTransferAmount] = useState('');
  const [selectedValidator, setSelectedValidator] = useState<string>('');

  // Add conversion functions
  const dtcToAphoton = (dtcAmount: string): string => {
    return (parseFloat(dtcAmount) * 1e18).toString();
  };

  const aphotonToDtc = (aphotonAmount: string): string => {
    return (parseFloat(aphotonAmount) / 1e18).toFixed(6);
  };

  // Load validators on mount
  useEffect(() => {
    loadValidators();
    if (wallet.address) {
      loadStakingInfo();
    }
  }, [wallet.address]);

  const loadValidators = async () => {
    try {
      const response = await fetch('/api/cosmos/validators');
      const data = await response.json();
      setValidators(data.validators || []);
    } catch (error) {
      console.error('Failed to load validators:', error);
    }
  };

  const loadStakingInfo = async () => {
    if (!wallet.address) return;
    
    try {
      const keyName = `staker_${wallet.address.slice(-8)}`;
      const response = await fetch(`/api/cosmos/staking-info?keyName=${keyName}&ethAddress=${wallet.address}`);
      const data = await response.json();
      
      if (data.success) {
        setStakingInfo(data.stakingInfo);
        setCosmosAddress(data.cosmosAddress);
        setCosmosBalance(data.cosmosBalance);
      }
    } catch (error) {
      console.error('Failed to load staking info:', error);
    }
  };

  const handleJoinValidator = async (validator: Validator) => {
    if (!wallet.address || !provider) return;

    setLoading(true);
    try {
      const message = `Join validator ${validator.moniker} for staking - ${wallet.address}`;
      const signer = await provider.getSigner();
      const signature = await signer.signMessage(message);

      const keyName = `staker_${wallet.address.slice(-8)}`;
      const result = await createCosmosKeyFromSignature(keyName, signature, validator.address, wallet.address);

      if (result.success) {
        alert(`✅ Đã tham gia ${validator.moniker}!\n\nKey: ${keyName}\nCosmos Address: ${result.cosmosAddress}`);
        await loadStakingInfo();
      } else {
        throw new Error(result.error || 'Failed to join validator');
      }
    } catch (error: any) {
      console.error('Failed to join validator:', error);
      alert(`❌ Lỗi: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleDelegate = async () => {
    if (!selectedValidator || !delegateAmount) return;

    setLoading(true);
    try {
      const aphotonAmount = dtcToAphoton(delegateAmount);
      
      const response = await fetch('/api/cosmos/delegate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ethAddress: wallet.address,
          validatorAddress: selectedValidator,
          amount: aphotonAmount
        })
      });

      const result = await response.json();
      if (result.success) {
        alert(`✅ Delegate thành công!\nTx: ${result.txHash}\nAmount: ${delegateAmount} DTC`);
        setDelegateAmount('');
        await loadStakingInfo();
      } else {
        throw new Error(result.error);
      }
    } catch (error: any) {
      alert(`❌ Lỗi delegate: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleUndelegate = async () => {
    if (!selectedValidator || !undelegateAmount) return;

    setLoading(true);
    try {
      const aphotonAmount = dtcToAphoton(undelegateAmount);
      
      const response = await fetch('/api/cosmos/undelegate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ethAddress: wallet.address,
          validatorAddress: selectedValidator,
          amount: aphotonAmount
        })
      });

      const result = await response.json();
      if (result.success) {
        alert(`✅ Undelegate thành công!\nTx: ${result.txHash}\nAmount: ${delegateAmount} DTC\n\n⏳ Tiền sẽ về sau 21 ngày unbonding period.`);
        setUndelegateAmount('');
        await loadStakingInfo();
      } else {
        throw new Error(result.error);
      }
    } catch (error: any) {
      alert(`❌ Lỗi undelegate: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleWithdrawRewards = async (validatorAddress?: string) => {
    setLoading(true);
    try {
      const response = await fetch('/api/cosmos/withdraw-rewards', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ethAddress: wallet.address,
          validatorAddress
        })
      });

      const result = await response.json();
      if (result.success) {
        alert(`✅ Withdraw rewards thành công!\nTx: ${result.txHash}\n\n💰 Rewards đã về cosmos address.`);
        await loadStakingInfo();
      } else {
        throw new Error(result.error);
      }
    } catch (error: any) {
      alert(`❌ Lỗi withdraw rewards: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleTransferToETH = async () => {
    if (!transferAmount) return;

    setLoading(true);
    try {
      const response = await fetch('/api/cosmos/transfer-to-eth', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ethAddress: wallet.address,
          amount: transferAmount
        })
      });

      const result = await response.json();
      if (result.success) {
        alert(`✅ Transfer về ETH thành công!\nTx: ${result.txHash}\nAmount: ${transferAmount} aphoton`);
        setTransferAmount('');
        await loadStakingInfo();
      } else {
        throw new Error(result.error);
      }
    } catch (error: any) {
      alert(`❌ Lỗi transfer: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleTransferToCosmos = async () => {
    if (!transferAmount || !provider) return;

    setLoading(true);
    try {
      const signer = await provider.getSigner();
      const tx = await signer.sendTransaction({
        to: cosmosAddress,
        value: ethers.parseEther(transferAmount)
      });

      await tx.wait();
      alert(`✅ Transfer to Cosmos thành công!\nTx: ${tx.hash}\nAmount: ${transferAmount} ETH`);
      setTransferAmount('');
      await loadStakingInfo();
    } catch (error: any) {
      alert(`❌ Lỗi transfer: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  if (!wallet.address) {
    return (
      <Card title="Validator Staking">
        <p className="text-center text-gray-500">Vui lòng kết nối ví để sử dụng tính năng staking</p>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Info */}
      <Card title="Staking Overview">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <p className="text-sm text-gray-500">ETH Address</p>
            <p className="font-mono text-sm">{wallet.address.slice(0, 10)}...{wallet.address.slice(-8)}</p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-500">Cosmos Address</p>
            <p className="font-mono text-sm">{cosmosAddress ? `${cosmosAddress.slice(0, 10)}...${cosmosAddress.slice(-8)}` : 'Not created'}</p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-500">Cosmos Balance</p>
            <p className="font-semibold">{parseFloat(cosmosBalance).toFixed(6)} aphoton</p>
          </div>
        </div>
      </Card>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        {[
          { key: 'validators', label: 'Validators' },
          { key: 'staking', label: 'My Staking' },
          { key: 'rewards', label: 'Rewards' },
          { key: 'transfer', label: 'Transfer' }
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === tab.key
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Validators Tab */}
      {activeTab === 'validators' && (
        <Card title="Available Validators">
          <div className="space-y-4">
            {validators.map((validator) => (
              <div key={validator.address} className="border rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold">{validator.moniker}</h3>
                    <p className="text-sm text-gray-500">Commission: {parseFloat(validator.commission).toFixed(2)}%</p>
                    <p className="text-sm text-gray-500">Tokens: {(parseFloat(validator.tokens) / 1e18).toFixed(0)}</p>
                  </div>
                  <Button
                    onClick={() => handleJoinValidator(validator)}
                    disabled={loading}
                    size="sm"
                  >
                    {loading ? <LoadingSpinner size="sm" /> : 'Join'}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Staking Tab */}
      {activeTab === 'staking' && (
        <div className="space-y-6">
          <Card title="Delegate Tokens">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Select Validator</label>
                <select
                  value={selectedValidator}
                  onChange={(e) => setSelectedValidator(e.target.value)}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="">Choose validator...</option>
                  {validators.map((v) => (
                    <option key={v.address} value={v.address}>{v.moniker}</option>
                  ))}
                </select>
              </div>
              <Input
                label="Amount (DTC)"
                value={delegateAmount}
                onChange={(e) => setDelegateAmount(e.target.value)}
                placeholder="1.0"
                helperText="Enter amount in DTC tokens"
              />
              <div className="text-xs text-gray-500">
                ≈ {delegateAmount ? dtcToAphoton(delegateAmount) : '0'} aphoton
              </div>
              <Button
                onClick={handleDelegate}
                disabled={loading || !selectedValidator || !delegateAmount}
                className="w-full"
              >
                {loading ? <LoadingSpinner size="sm" /> : 'Delegate'}
              </Button>
            </div>
          </Card>

          <Card title="Undelegate Tokens">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Select Validator</label>
                <select
                  value={selectedValidator}
                  onChange={(e) => setSelectedValidator(e.target.value)}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="">Choose validator...</option>
                  {validators.map((v) => (
                    <option key={v.address} value={v.address}>{v.moniker}</option>
                  ))}
                </select>
              </div>
              <Input
                label="Amount (DTC)"
                value={undelegateAmount}
                onChange={(e) => setUndelegateAmount(e.target.value)}
                placeholder="1.0"
                helperText="Enter amount in DTC tokens"
              />
              <div className="text-xs text-gray-500">
                ≈ {undelegateAmount ? dtcToAphoton(undelegateAmount) : '0'} aphoton
              </div>
              <Button
                onClick={handleUndelegate}
                disabled={loading || !selectedValidator || !undelegateAmount}
                variant="secondary"
                className="w-full"
              >
                {loading ? <LoadingSpinner size="sm" /> : 'Undelegate'}
              </Button>
            </div>
          </Card>

          {/* Current Delegations - show in DTC */}
          {stakingInfo && (
            <Card title="My Delegations">
              <div className="space-y-2">
                <p>Total Delegated: {aphotonToDtc(stakingInfo.delegated)} DTC</p>
                {stakingInfo.unbonding.length > 0 && (
                  <div>
                    <p className="font-medium">Unbonding:</p>
                    {stakingInfo.unbonding.map((ub, i) => (
                      <p key={i} className="text-sm text-gray-600">
                        {aphotonToDtc(ub.amount)} DTC - Complete: {new Date(ub.completionTime).toLocaleDateString()}
                      </p>
                    ))}
                  </div>
                )}
              </div>
            </Card>
          )}
        </div>
      )}

      {/* Rewards Tab */}
      {activeTab === 'rewards' && (
        <div className="space-y-6">
          <Card title="Staking Rewards">
            <div className="space-y-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">
                  {stakingInfo ? aphotonToDtc(stakingInfo.rewards) : '0.000000'} DTC
                </p>
                <p className="text-sm text-gray-500">Available Rewards</p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button
                  onClick={() => handleWithdrawRewards()}
                  disabled={loading}
                  className="w-full"
                >
                  {loading ? <LoadingSpinner size="sm" /> : 'Withdraw All Rewards'}
                </Button>
                
                <Button
                  onClick={() => handleWithdrawRewards(selectedValidator)}
                  disabled={loading || !selectedValidator}
                  variant="secondary"
                  className="w-full"
                >
                  {loading ? <LoadingSpinner size="sm" /> : 'Withdraw from Selected'}
                </Button>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Select Validator for Specific Withdrawal</label>
                <select
                  value={selectedValidator}
                  onChange={(e) => setSelectedValidator(e.target.value)}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="">Choose validator...</option>
                  {validators.map((v) => (
                    <option key={v.address} value={v.address}>{v.moniker}</option>
                  ))}
                </select>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Transfer Tab */}
      {activeTab === 'transfer' && (
        <div className="space-y-6">
          <Card title="Transfer ETH to Cosmos">
            <div className="space-y-4">
              <Input
                label="Amount (ETH)"
                value={transferAmount}
                onChange={(e) => setTransferAmount(e.target.value)}
                placeholder="0.1"
              />
              <Button
                onClick={handleTransferToCosmos}
                disabled={loading || !transferAmount || !cosmosAddress}
                className="w-full"
              >
                {loading ? <LoadingSpinner size="sm" /> : 'Transfer to Cosmos'}
              </Button>
            </div>
          </Card>

          <Card title="Transfer Cosmos to ETH">
            <div className="space-y-4">
              <Input
                label="Amount (aphoton)"
                value={transferAmount}
                onChange={(e) => setTransferAmount(e.target.value)}
                placeholder="1000000000000000000"
              />
              <Button
                onClick={handleTransferToETH}
                disabled={loading || !transferAmount}
                variant="secondary"
                className="w-full"
              >
                {loading ? <LoadingSpinner size="sm" /> : 'Transfer to ETH'}
              </Button>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}


