# Price Synchronization Update - WalletConnect & Dashboard

## Vấn đề
Trước đây, WalletConnect và Dashboard hiển thị giá token không đồng bộ:
- **Dashboard:** Hiển thị giá token từ `tokenStats` 
- **WalletConnect:** Không hiển thị giá hoặc tính toán riêng biệt
- **Inconsistency:** Người dùng thấy thông tin giá khác nhau ở các component

## Giải pháp đã triển khai

### 1. Tạo Hook `useTokenPrice`
**File:** `app/hooks/useTokenPrice.ts`

#### Tính năng chính:
- **Centralized Price Management:** Quản lý giá tập trung cho cả DTC và DTCERC
- **Real-time Updates:** Cập nhật giá mỗi 30 giây
- **Consistent Data:** <PERSON><PERSON>m bảo tất cả components sử dụng cùng dữ liệu giá
- **USD Calculation:** Tính toán giá trị USD cho portfolio

```typescript
export interface TokenPriceData {
  dtcPrice: number;      // Giá DTC (Native)
  dtcercPrice: number;   // Giá DTCERC (ERC20)
  lastUpdated: Date;     // Thời gian cập nhật cuối
}

export const useTokenPrice = () => {
  // State management
  const [priceData, setPriceData] = useState<TokenPriceData>({
    dtcPrice: 1.0,
    dtcercPrice: 1.0,
    lastUpdated: new Date(),
  });

  // Utility functions
  const calculateUSDValue = (amount: string, tokenType: 'dtc' | 'dtcerc'): number => {
    const numAmount = parseFloat(amount) || 0;
    const price = tokenType === 'dtc' ? priceData.dtcPrice : priceData.dtcercPrice;
    return numAmount * price;
  };

  const calculateTotalValue = (dtcAmount: string, dtcercAmount: string): number => {
    return calculateUSDValue(dtcAmount, 'dtc') + calculateUSDValue(dtcercAmount, 'dtcerc');
  };

  return { priceData, calculateUSDValue, calculateTotalValue };
};
```

### 2. Cập nhật WalletConnect
**File:** `app/components/WalletConnect.tsx`

#### Thay đổi chính:
- **Import Hook:** Thêm `useTokenPrice` hook
- **USD Display:** Hiển thị tổng giá trị portfolio bằng USD
- **Consistent Calculation:** Sử dụng cùng logic tính giá với Dashboard

#### Trước:
```typescript
<div className="text-xs text-green-600 dark:text-green-400">
  <div>{nativeBalance.formattedBalance} {nativeBalance.symbol}</div>
  <div>{tokenBalance.formattedBalance} {config.TOKEN_SYMBOL}</div>
</div>
```

#### Sau:
```typescript
<div className="text-xs text-green-600 dark:text-green-400">
  <div>{nativeBalance.formattedBalance} {nativeBalance.symbol}</div>
  <div>{tokenBalance.formattedBalance} {config.TOKEN_SYMBOL}</div>
  <div className="text-gray-500 dark:text-gray-400 mt-1">
    ≈ ${calculateTotalValue(nativeBalance.formattedBalance, tokenBalance.formattedBalance).toFixed(2)}
  </div>
</div>
```

### 3. Cập nhật Dashboard
**File:** `app/components/Dashboard.tsx`

#### Thay đổi chính:
- **Import Hook:** Thêm `useTokenPrice` hook
- **Enhanced Price Display:** Hiển thị giá cả DTC và DTCERC
- **Real-time Updates:** Giá cập nhật tự động mỗi 30 giây

#### Trước:
```typescript
<p className="text-2xl font-bold text-gray-900 dark:text-white">
  ${tokenStats?.price.toFixed(2) || '1.00'}
</p>
```

#### Sau:
```typescript
<p className="text-2xl font-bold text-gray-900 dark:text-white">
  ${priceData.dtcercPrice.toFixed(2)}
</p>
<p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
  DTC: ${priceData.dtcPrice.toFixed(2)}
</p>
```

## Tính năng mới

### 1. Synchronized Price Display
- ✅ **WalletConnect:** Hiển thị tổng giá trị portfolio
- ✅ **Dashboard:** Hiển thị giá riêng lẻ cho từng token
- ✅ **Real-time:** Cập nhật đồng thời trên tất cả components

### 2. Enhanced User Experience
- ✅ **Portfolio Value:** Người dùng thấy tổng giá trị trong WalletConnect
- ✅ **Individual Prices:** Chi tiết giá từng token trong Dashboard
- ✅ **Consistency:** Không còn thông tin mâu thuẫn

### 3. Technical Benefits
- ✅ **Single Source of Truth:** Một hook quản lý tất cả giá
- ✅ **Reusable Logic:** Có thể sử dụng trong components khác
- ✅ **Performance:** Tránh duplicate API calls
- ✅ **Maintainability:** Dễ dàng cập nhật logic giá

## Hiển thị so sánh

### WalletConnect (Header):
```
┌─────────────────────────────────┐
│ 🟢 abc123...def4                │
│ 1,234.56 DTC                    │
│ 5,678.90 DTCERC                 │
│ ≈ $6,913.46                     │ ← NEW
└─────────────────────────────────┘
```

### Dashboard (Token Price Card):
```
┌─────────────────────────────────┐
│ 📈 Token Price                  │
│ $1.00                           │ ← DTCERC Price
│ DTC: $1.00                      │ ← NEW: DTC Price
└─────────────────────────────────┘
```

## Mock Price Logic

### Current Implementation:
```typescript
const mockPrices: TokenPriceData = {
  dtcPrice: 1.0 + (Math.random() * 0.1 - 0.05),    // $1.00 ± 5%
  dtcercPrice: 1.0 + (Math.random() * 0.1 - 0.05), // $1.00 ± 5%
  lastUpdated: new Date(),
};
```

### Future Integration:
- **Price Oracle:** Kết nối với Chainlink hoặc API giá thực
- **DEX Integration:** Lấy giá từ Uniswap/PancakeSwap
- **Multiple Sources:** Aggregate từ nhiều nguồn
- **Historical Data:** Lưu trữ lịch sử giá

## Auto-Update Mechanism

### Update Frequency:
- **Interval:** 30 giây
- **On Mount:** Fetch ngay khi component mount
- **Error Handling:** Fallback về giá mặc định nếu lỗi

### Performance Optimization:
- **Shared State:** Tất cả components dùng chung một instance
- **Debouncing:** Tránh quá nhiều requests
- **Caching:** Cache giá trong memory

## Cách sử dụng Hook

### Basic Usage:
```typescript
import { useTokenPrice } from '../hooks/useTokenPrice';

const MyComponent = () => {
  const { priceData, calculateTotalValue } = useTokenPrice();
  
  return (
    <div>
      <p>DTC Price: ${priceData.dtcPrice.toFixed(2)}</p>
      <p>DTCERC Price: ${priceData.dtcercPrice.toFixed(2)}</p>
      <p>Total Value: ${calculateTotalValue('100', '200').toFixed(2)}</p>
    </div>
  );
};
```

### Advanced Usage:
```typescript
const { 
  priceData, 
  loading, 
  error, 
  fetchTokenPrices,
  calculateUSDValue 
} = useTokenPrice();

// Manual refresh
const handleRefresh = () => {
  fetchTokenPrices();
};

// Calculate individual token value
const dtcValue = calculateUSDValue('100', 'dtc');
const dtcercValue = calculateUSDValue('200', 'dtcerc');
```

## Testing

### Test Cases:
1. **Price Sync:** Verify cùng giá hiển thị ở WalletConnect và Dashboard
2. **Auto Update:** Kiểm tra giá cập nhật mỗi 30 giây
3. **Error Handling:** Test fallback khi API lỗi
4. **Calculation:** Verify tính toán USD chính xác

### Manual Testing:
1. Connect wallet và xem giá trong WalletConnect
2. Navigate to Dashboard và so sánh giá
3. Wait 30 giây để xem auto-update
4. Refresh page và kiểm tra consistency

## Future Enhancements

### Short-term:
- **Price Change Indicators:** Hiển thị % thay đổi 24h
- **Price Charts:** Mini charts trong components
- **Currency Options:** Support EUR, JPY, etc.

### Long-term:
- **Real Price Feeds:** Integration với price oracles
- **Historical Analysis:** Price trends và analytics
- **Portfolio Tracking:** Advanced portfolio management
- **Price Alerts:** Notifications khi giá thay đổi

## Troubleshooting

### Common Issues:
1. **Prices not updating:** Check console for errors, verify hook import
2. **Different prices shown:** Clear browser cache, hard refresh
3. **USD calculation wrong:** Verify balance values are numbers

### Debug Commands:
```javascript
// Check current prices
console.log('Price Data:', priceData);

// Test calculations
console.log('Total Value:', calculateTotalValue('100', '200'));

// Force refresh
fetchTokenPrices();
```
